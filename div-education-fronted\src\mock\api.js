// Mock API服务 - DIV教育学习平台
import {
  users,
  courses,
  categories,
  posts,
  comments,
  studyGroups,
  news,
  learningRecords,
  experiments,
  experimentRecords,
  learningAnalytics
} from './data.js';

// 模拟网络延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms));

// 生成响应格式
const createResponse = (data, message = 'success') => ({
  code: 200,
  message,
  data,
  timestamp: new Date().toISOString()
});

// 用户相关API
export const userAPI = {
  // 用户登录
  async login(email, password) {
    await delay();
    const user = users.find(u => u.email === email);
    if (user) {
      return createResponse({
        user,
        token: 'mock-jwt-token-' + user.id
      }, '登录成功');
    }
    throw new Error('用户名或密码错误');
  },

  // 用户注册
  async register(userData) {
    await delay();
    const newUser = {
      id: users.length + 1,
      ...userData,
      role: 'student',
      joinDate: new Date().toISOString().split('T')[0],
      learningHours: 0,
      completedCourses: 0,
      points: 0,
      level: '新手学员'
    };
    users.push(newUser);
    return createResponse(newUser, '注册成功');
  },

  // 获取用户信息
  async getProfile(userId) {
    await delay();
    const user = users.find(u => u.id === userId);
    if (user) {
      return createResponse(user);
    }
    throw new Error('用户不存在');
  },

  // 更新用户信息
  async updateProfile(userId, updateData) {
    await delay();
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
      users[userIndex] = { ...users[userIndex], ...updateData };
      return createResponse(users[userIndex], '更新成功');
    }
    throw new Error('用户不存在');
  }
};

// 课程相关API
export const courseAPI = {
  // 获取课程列表
  async getCourses(params = {}) {
    await delay();
    let filteredCourses = [...courses];
    
    // 分类筛选
    if (params.categoryId) {
      filteredCourses = filteredCourses.filter(c => c.categoryId === params.categoryId);
    }
    
    // 搜索
    if (params.keyword) {
      filteredCourses = filteredCourses.filter(c => 
        c.title.includes(params.keyword) || 
        c.description.includes(params.keyword)
      );
    }
    
    // 分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    
    return createResponse({
      list: filteredCourses.slice(start, end),
      total: filteredCourses.length,
      page,
      pageSize
    });
  },

  // 获取课程详情
  async getCourseDetail(courseId) {
    await delay();
    const course = courses.find(c => c.id === courseId);
    if (course) {
      return createResponse(course);
    }
    throw new Error('课程不存在');
  },

  // 获取课程分类
  async getCategories() {
    await delay();
    return createResponse(categories);
  },

  // 添加课程
  async addCourse(courseData) {
    await delay();
    const newCourse = {
      id: courses.length + 1,
      ...courseData,
      studentsCount: 0,
      rating: 0,
      createdAt: new Date().toISOString().split('T')[0]
    };
    courses.push(newCourse);
    return createResponse(newCourse, '课程添加成功');
  },

  // 更新课程
  async updateCourse(courseId, updateData) {
    await delay();
    const courseIndex = courses.findIndex(c => c.id === courseId);
    if (courseIndex !== -1) {
      courses[courseIndex] = { ...courses[courseIndex], ...updateData };
      return createResponse(courses[courseIndex], '课程更新成功');
    }
    throw new Error('课程不存在');
  },

  // 删除课程
  async deleteCourse(courseId) {
    await delay();
    const courseIndex = courses.findIndex(c => c.id === courseId);
    if (courseIndex !== -1) {
      courses.splice(courseIndex, 1);
      return createResponse(null, '课程删除成功');
    }
    throw new Error('课程不存在');
  }
};

// 社区相关API
export const communityAPI = {
  // 获取帖子列表
  async getPosts(params = {}) {
    await delay();
    let filteredPosts = [...posts];
    
    // 搜索
    if (params.keyword) {
      filteredPosts = filteredPosts.filter(p => 
        p.title.includes(params.keyword) || 
        p.content.includes(params.keyword)
      );
    }
    
    // 分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    
    return createResponse({
      list: filteredPosts.slice(start, end),
      total: filteredPosts.length,
      page,
      pageSize
    });
  },

  // 获取帖子详情
  async getPostDetail(postId) {
    await delay();
    const post = posts.find(p => p.id === postId);
    if (post) {
      return createResponse(post);
    }
    throw new Error('帖子不存在');
  },

  // 发布帖子
  async createPost(postData) {
    await delay();
    const newPost = {
      id: posts.length + 1,
      ...postData,
      likesCount: 0,
      commentsCount: 0,
      viewsCount: 0,
      isLiked: false,
      createdAt: new Date().toISOString()
    };
    posts.push(newPost);
    return createResponse(newPost, '帖子发布成功');
  },

  // 点赞帖子
  async likePost(postId) {
    await delay();
    const post = posts.find(p => p.id === postId);
    if (post) {
      post.isLiked = !post.isLiked;
      post.likesCount += post.isLiked ? 1 : -1;
      return createResponse(post, post.isLiked ? '点赞成功' : '取消点赞');
    }
    throw new Error('帖子不存在');
  },

  // 获取评论列表
  async getComments(postId) {
    await delay();
    const postComments = comments.filter(c => c.postId === postId);
    return createResponse(postComments);
  },

  // 发表评论
  async createComment(commentData) {
    await delay();
    const newComment = {
      id: comments.length + 1,
      ...commentData,
      createdAt: new Date().toISOString()
    };
    comments.push(newComment);
    
    // 更新帖子评论数
    const post = posts.find(p => p.id === commentData.postId);
    if (post) {
      post.commentsCount++;
    }
    
    return createResponse(newComment, '评论发表成功');
  }
};

// 学习小组相关API
export const groupAPI = {
  // 获取小组列表
  async getGroups(params = {}) {
    await delay();
    let filteredGroups = [...studyGroups];
    
    // 搜索
    if (params.keyword) {
      filteredGroups = filteredGroups.filter(g => 
        g.name.includes(params.keyword) || 
        g.description.includes(params.keyword)
      );
    }
    
    return createResponse(filteredGroups);
  },

  // 获取小组详情
  async getGroupDetail(groupId) {
    await delay();
    const group = studyGroups.find(g => g.id === groupId);
    if (group) {
      return createResponse(group);
    }
    throw new Error('小组不存在');
  },

  // 创建小组
  async createGroup(groupData) {
    await delay();
    const newGroup = {
      id: studyGroups.length + 1,
      ...groupData,
      memberCount: 1,
      createdAt: new Date().toISOString().split('T')[0],
      recentActivity: '刚刚创建'
    };
    studyGroups.push(newGroup);
    return createResponse(newGroup, '小组创建成功');
  }
};

// 简讯相关API
export const newsAPI = {
  // 获取简讯列表
  async getNews(params = {}) {
    await delay();
    let filteredNews = [...news];
    
    // 分类筛选
    if (params.category) {
      filteredNews = filteredNews.filter(n => n.category === params.category);
    }
    
    return createResponse(filteredNews);
  },

  // 获取简讯详情
  async getNewsDetail(newsId) {
    await delay();
    const newsItem = news.find(n => n.id === newsId);
    if (newsItem) {
      return createResponse(newsItem);
    }
    throw new Error('简讯不存在');
  }
};

// 学习记录相关API
export const learningAPI = {
  // 记录学习进度
  async recordProgress(userId, courseId, progress, position) {
    await delay();
    const existingRecord = learningRecords.find(r => r.userId === userId && r.courseId === courseId);

    if (existingRecord) {
      existingRecord.progress = progress;
      existingRecord.lastPosition = position;
      existingRecord.updatedAt = new Date().toISOString();
      if (progress >= 100) {
        existingRecord.completedAt = new Date().toISOString();
      }
      return createResponse(existingRecord, '学习进度已更新');
    } else {
      const newRecord = {
        id: learningRecords.length + 1,
        userId,
        courseId,
        progress,
        lastPosition: position,
        completedAt: progress >= 100 ? new Date().toISOString() : null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      learningRecords.push(newRecord);
      return createResponse(newRecord, '学习记录已创建');
    }
  },

  // 获取用户学习记录
  async getUserLearningRecords(userId) {
    await delay();
    const userRecords = learningRecords.filter(r => r.userId === userId);
    return createResponse(userRecords);
  }
};

// 仿真实验相关API
export const experimentAPI = {
  // 获取实验列表
  async getExperiments(params = {}) {
    await delay();
    let filteredExperiments = [...experiments];

    // 学科筛选
    if (params.subject) {
      filteredExperiments = filteredExperiments.filter(e => e.subject === params.subject);
    }

    // 难度筛选
    if (params.difficulty) {
      filteredExperiments = filteredExperiments.filter(e => e.difficulty === params.difficulty);
    }

    // 搜索
    if (params.keyword) {
      filteredExperiments = filteredExperiments.filter(e =>
        e.title.includes(params.keyword) ||
        e.description.includes(params.keyword)
      );
    }

    return createResponse(filteredExperiments);
  },

  // 获取实验详情
  async getExperimentDetail(experimentId) {
    await delay();
    const experiment = experiments.find(e => e.id === experimentId);
    if (experiment) {
      return createResponse(experiment);
    }
    throw new Error('实验不存在');
  },

  // 开始实验
  async startExperiment(userId, experimentId) {
    await delay();
    const newRecord = {
      id: experimentRecords.length + 1,
      userId,
      experimentId,
      status: 'in_progress',
      score: null,
      timeSpent: 0,
      attempts: 1,
      data: {},
      completedAt: null,
      createdAt: new Date().toISOString()
    };
    experimentRecords.push(newRecord);
    return createResponse(newRecord, '实验已开始');
  },

  // 提交实验数据
  async submitExperimentData(recordId, data) {
    await delay();
    const record = experimentRecords.find(r => r.id === recordId);
    if (record) {
      record.data = { ...record.data, ...data };
      record.timeSpent += 5; // 模拟时间增加
      return createResponse(record, '实验数据已保存');
    }
    throw new Error('实验记录不存在');
  },

  // 完成实验
  async completeExperiment(recordId, finalData) {
    await delay();
    const record = experimentRecords.find(r => r.id === recordId);
    if (record) {
      record.status = 'completed';
      record.data = { ...record.data, ...finalData };
      record.score = Math.floor(Math.random() * 30) + 70; // 70-100分
      record.completedAt = new Date().toISOString();
      return createResponse(record, '实验完成');
    }
    throw new Error('实验记录不存在');
  },

  // 获取用户实验记录
  async getUserExperimentRecords(userId) {
    await delay();
    const userRecords = experimentRecords.filter(r => r.userId === userId);
    return createResponse(userRecords);
  }
};

// 数据分析相关API
export const analyticsAPI = {
  // 获取学习分析数据
  async getLearningAnalytics(userId) {
    await delay();
    // 模拟根据用户ID返回不同数据
    const analytics = { ...learningAnalytics, userId };
    return createResponse(analytics);
  },

  // 获取能力评估报告
  async getSkillAssessment(userId) {
    await delay();
    return createResponse(learningAnalytics.skillAssessment);
  },

  // 获取学习路径
  async getLearningPath(userId) {
    await delay();
    return createResponse(learningAnalytics.learningPath);
  },

  // 获取周活动数据
  async getWeeklyActivity(userId) {
    await delay();
    return createResponse(learningAnalytics.weeklyActivity);
  },

  // 获取月度进度数据
  async getMonthlyProgress(userId) {
    await delay();
    return createResponse(learningAnalytics.monthlyProgress);
  }
};
