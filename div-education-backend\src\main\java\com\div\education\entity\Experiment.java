package com.div.education.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 实验实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "experiments")
public class Experiment extends BaseEntity {

    @Column(name = "title", nullable = false, length = 200)
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "cover_image", length = 500)
    private String coverImage;

    @Column(name = "subject", nullable = false, length = 50)
    private String subject; // 学科：物理、化学

    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty", nullable = false)
    private ExperimentDifficulty difficulty;

    @Column(name = "duration", nullable = false)
    private Integer duration; // 实验时长（分钟）

    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating;

    @Column(name = "completed_count", nullable = false)
    private Integer completedCount = 0;

    @Column(name = "objectives", columnDefinition = "TEXT")
    private String objectives; // 实验目标

    @Column(name = "equipment", columnDefinition = "TEXT")
    private String equipment; // 实验器材

    @Column(name = "steps", columnDefinition = "TEXT")
    private String steps; // 实验步骤

    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags; // 标签

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ExperimentStatus status = ExperimentStatus.ACTIVE;

    /**
     * 实验难度枚举
     */
    public enum ExperimentDifficulty {
        BEGINNER("初级"),
        INTERMEDIATE("中级"),
        ADVANCED("高级");

        private final String description;

        ExperimentDifficulty(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 实验状态枚举
     */
    public enum ExperimentStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        MAINTENANCE("维护中");

        private final String description;

        ExperimentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
