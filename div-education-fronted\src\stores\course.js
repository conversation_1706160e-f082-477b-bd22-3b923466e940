import { defineStore } from 'pinia'
import { api } from '../api/index.js'

export const useCourseStore = defineStore('course', {
  state: () => ({
    // 课程列表
    courses: [],
    // 当前课程
    currentCourse: null,
    // 课程分类
    categories: [],
    // 学习进度记录
    learningProgress: {},
    // 加载状态
    loading: false,
    // 分页信息
    pagination: {
      page: 1,
      pageSize: 12,
      total: 0
    }
  }),

  getters: {
    // 获取热门课程
    popularCourses: (state) => {
      return state.courses
        .filter(course => course.studentsCount > 500)
        .sort((a, b) => b.studentsCount - a.studentsCount)
        .slice(0, 6)
    },
    
    // 获取最新课程
    latestCourses: (state) => {
      return state.courses
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 6)
    },
    
    // 根据分类获取课程
    getCoursesByCategory: (state) => (categoryId) => {
      return state.courses.filter(course => course.categoryId === categoryId)
    }
  },

  actions: {
    // 获取课程列表
    async fetchCourses(params = {}) {
      this.loading = true
      try {
        const response = await api.courses.getCourses(
          this.pagination.page - 1, // 后端使用0基索引
          this.pagination.pageSize,
          params.category,
          params.level
        )
        
        this.courses = response.data.list
        this.pagination.total = response.data.total
        this.pagination.page = response.data.page
        
        return response
      } catch (error) {
        console.error('获取课程列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取课程详情
    async fetchCourseDetail(courseId) {
      this.loading = true
      try {
        const response = await api.courses.getCourseById(courseId)
        this.currentCourse = response.data
        return response
      } catch (error) {
        console.error('获取课程详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取课程分类
    async fetchCategories() {
      try {
        const response = await courseAPI.getCategories()
        this.categories = response.data
        return response
      } catch (error) {
        console.error('获取课程分类失败:', error)
        throw error
      }
    },

    // 添加课程
    async addCourse(courseData) {
      try {
        const response = await courseAPI.addCourse(courseData)
        // 重新获取课程列表
        await this.fetchCourses()
        return response
      } catch (error) {
        console.error('添加课程失败:', error)
        throw error
      }
    },

    // 更新课程
    async updateCourse(courseId, updateData) {
      try {
        const response = await courseAPI.updateCourse(courseId, updateData)
        
        // 更新本地课程列表
        const index = this.courses.findIndex(c => c.id === courseId)
        if (index !== -1) {
          this.courses[index] = response.data
        }
        
        // 如果是当前课程，也要更新
        if (this.currentCourse?.id === courseId) {
          this.currentCourse = response.data
        }
        
        return response
      } catch (error) {
        console.error('更新课程失败:', error)
        throw error
      }
    },

    // 删除课程
    async deleteCourse(courseId) {
      try {
        const response = await courseAPI.deleteCourse(courseId)
        
        // 从本地课程列表中移除
        this.courses = this.courses.filter(c => c.id !== courseId)
        
        // 如果删除的是当前课程，清空当前课程
        if (this.currentCourse?.id === courseId) {
          this.currentCourse = null
        }
        
        return response
      } catch (error) {
        console.error('删除课程失败:', error)
        throw error
      }
    },

    // 记录学习进度
    async recordLearningProgress(userId, courseId, progress, position) {
      try {
        const response = await learningAPI.recordProgress(userId, courseId, progress, position)
        
        // 更新本地学习进度
        const key = `${userId}-${courseId}`
        this.learningProgress[key] = {
          progress,
          position,
          updatedAt: new Date().toISOString()
        }
        
        return response
      } catch (error) {
        console.error('记录学习进度失败:', error)
        throw error
      }
    },

    // 获取用户学习记录
    async fetchUserLearningRecords(userId) {
      try {
        const response = await learningAPI.getUserLearningRecords(userId)
        
        // 转换为便于查找的格式
        const progressMap = {}
        response.data.forEach(record => {
          const key = `${record.userId}-${record.courseId}`
          progressMap[key] = {
            progress: record.progress,
            position: record.lastPosition,
            completedAt: record.completedAt,
            updatedAt: record.updatedAt
          }
        })
        
        this.learningProgress = progressMap
        return response
      } catch (error) {
        console.error('获取学习记录失败:', error)
        throw error
      }
    },

    // 搜索课程
    async searchCourses(keyword) {
      return await this.fetchCourses({ keyword })
    },

    // 按分类筛选课程
    async filterCoursesByCategory(categoryId) {
      return await this.fetchCourses({ categoryId })
    },

    // 设置分页
    setPagination(page, pageSize) {
      this.pagination.page = page
      this.pagination.pageSize = pageSize
    },

    // 重置课程状态
    resetCourseState() {
      this.courses = []
      this.currentCourse = null
      this.pagination = {
        page: 1,
        pageSize: 12,
        total: 0
      }
    }
  }
})
