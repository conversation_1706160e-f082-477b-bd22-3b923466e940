package com.div.education.controller;

import com.div.education.dto.ApiResponse;
import com.div.education.dto.UserDTO;
import com.div.education.entity.User;
import com.div.education.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AuthController {

    private final UserService userService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request) {
        try {
            // 查找用户
            User user = userService.findByEmail(request.getEmail())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 验证密码
            if (!userService.validatePassword(user, request.getPassword())) {
                return ApiResponse.error(401, "密码错误");
            }

            // 检查用户状态
            if (user.getStatus() != User.UserStatus.ACTIVE) {
                return ApiResponse.error(403, "账号已被禁用");
            }

            // 生成简单的token（实际项目中应使用JWT）
            String token = "token_" + user.getId() + "_" + System.currentTimeMillis();
            
            LoginResponse response = new LoginResponse(token, UserDTO.fromEntity(user));
            return ApiResponse.success("登录成功", response);
            
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<UserDTO> register(@RequestBody RegisterRequest request) {
        try {
            User user = userService.createUser(
                    request.getUsername(),
                    request.getEmail(),
                    request.getPassword(),
                    request.getNickname(),
                    request.getRole()
            );
            
            return ApiResponse.success("注册成功", UserDTO.fromEntity(user));
            
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = userService.findByEmail(email).isPresent();
        return ApiResponse.success(exists);
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.findByUsername(username).isPresent();
        return ApiResponse.success(exists);
    }

    /**
     * 登录请求DTO
     */
    public static class LoginRequest {
        private String email;
        private String password;

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }

    /**
     * 注册请求DTO
     */
    public static class RegisterRequest {
        private String username;
        private String email;
        private String password;
        private String nickname;
        private User.UserRole role = User.UserRole.STUDENT;

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }
        public User.UserRole getRole() { return role; }
        public void setRole(User.UserRole role) { this.role = role; }
    }

    /**
     * 登录响应DTO
     */
    public static class LoginResponse {
        private String token;
        private UserDTO user;

        public LoginResponse(String token, UserDTO user) {
            this.token = token;
            this.user = user;
        }

        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }
        public UserDTO getUser() { return user; }
        public void setUser(UserDTO user) { this.user = user; }
    }
}
