<template>
  <div class="analytics-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>数据分析</h1>
        <p>深入了解您的学习行为，获得个性化的能力评估报告</p>
      </div>

      <!-- 总览统计 -->
      <div class="overview-stats">
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon size="32"><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ analytics.totalStudyTime }}</h3>
            <p>总学习时长(小时)</p>
          </div>
        </div>
        
        <div class="stat-card success">
          <div class="stat-icon">
            <el-icon size="32"><Medal /></el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ analytics.coursesCompleted }}</h3>
            <p>完成课程</p>
          </div>
        </div>
        
        <div class="stat-card warning">
          <div class="stat-icon">
            <el-icon size="32"><Experiment /></el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ analytics.experimentsCompleted }}</h3>
            <p>完成实验</p>
          </div>
        </div>
        
        <div class="stat-card info">
          <div class="stat-icon">
            <el-icon size="32"><Trophy /></el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ analytics.averageScore }}</h3>
            <p>平均分数</p>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <div class="chart-row">
          <!-- 周活动图表 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>本周学习活动</h3>
              <el-button type="primary" link @click="refreshWeeklyData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            <div class="chart-content">
              <div ref="weeklyChartRef" class="chart-container"></div>
            </div>
          </div>
          
          <!-- 月度进度图表 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>月度学习进度</h3>
              <el-button type="primary" link @click="refreshMonthlyData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            <div class="chart-content">
              <div ref="monthlyChartRef" class="chart-container"></div>
            </div>
          </div>
        </div>
        
        <!-- 技能评估雷达图 -->
        <div class="chart-card full-width">
          <div class="chart-header">
            <h3>技能能力评估</h3>
            <div class="skill-legend">
              <span class="legend-item">
                <span class="legend-color current"></span>
                当前水平
              </span>
              <span class="legend-item">
                <span class="legend-color progress"></span>
                近期进步
              </span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="skillChartRef" class="chart-container large"></div>
          </div>
        </div>
      </div>

      <!-- 学习路径 -->
      <div class="learning-path-section">
        <div class="section-header">
          <h3>学习路径</h3>
          <p>根据您的学习情况，为您推荐的学习路径</p>
        </div>
        
        <div class="path-timeline">
          <div
            v-for="(item, index) in analytics.learningPath"
            :key="index"
            class="path-item"
            :class="{ completed: item.completed, current: !item.completed && index === currentPathIndex }"
          >
            <div class="path-icon">
              <el-icon v-if="item.completed" class="completed-icon"><Check /></el-icon>
              <el-icon v-else-if="!item.completed && index === currentPathIndex" class="current-icon"><Clock /></el-icon>
              <el-icon v-else class="pending-icon"><Lock /></el-icon>
            </div>
            
            <div class="path-content">
              <h4>{{ item.skill }}</h4>
              <div v-if="item.completed" class="path-score">
                完成 - 得分: {{ item.score }}
              </div>
              <div v-else class="path-progress">
                <span>进度: {{ item.progress }}%</span>
                <el-progress :percentage="item.progress" :show-text="false" />
              </div>
            </div>
            
            <div v-if="index < analytics.learningPath.length - 1" class="path-connector"></div>
          </div>
        </div>
      </div>

      <!-- 详细报告 -->
      <div class="report-section">
        <div class="section-header">
          <h3>学习行为分析报告</h3>
          <el-button type="primary" @click="generateReport">
            <el-icon><Document /></el-icon>
            生成完整报告
          </el-button>
        </div>
        
        <div class="report-cards">
          <div class="report-card">
            <div class="report-icon">
              <el-icon size="24"><Calendar /></el-icon>
            </div>
            <div class="report-content">
              <h4>学习习惯</h4>
              <p>您已连续学习 <strong>{{ analytics.studyStreak }}</strong> 天，保持了良好的学习习惯。建议继续保持每日学习的节奏。</p>
            </div>
          </div>
          
          <div class="report-card">
            <div class="report-icon">
              <el-icon size="24"><TrendCharts /></el-icon>
            </div>
            <div class="report-content">
              <h4>学习效率</h4>
              <p>您的平均学习效率较高，完成课程的速度比平均水平快 <strong>20%</strong>。建议适当增加实践项目。</p>
            </div>
          </div>
          
          <div class="report-card">
            <div class="report-icon">
              <el-icon size="24"><Star /></el-icon>
            </div>
            <div class="report-content">
              <h4>优势领域</h4>
              <p>您在 <strong>前端开发</strong> 和 <strong>算法</strong> 方面表现突出，建议继续深入学习相关高级内容。</p>
            </div>
          </div>
          
          <div class="report-card">
            <div class="report-icon">
              <el-icon size="24"><Warning /></el-icon>
            </div>
            <div class="report-content">
              <h4>改进建议</h4>
              <p>建议加强 <strong>数据库设计</strong> 和 <strong>设计</strong> 方面的学习，这些技能对全栈开发很重要。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useUserStore } from '../stores/user.js'
import { analyticsAPI } from '../mock/api.js'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

const loading = ref(false)
const analytics = ref({
  totalStudyTime: 0,
  coursesCompleted: 0,
  experimentsCompleted: 0,
  averageScore: 0,
  studyStreak: 0,
  weeklyActivity: [],
  monthlyProgress: [],
  skillAssessment: {},
  learningPath: []
})

// 图表容器引用
const weeklyChartRef = ref()
const monthlyChartRef = ref()
const skillChartRef = ref()

// 当前学习路径索引
const currentPathIndex = computed(() => {
  return analytics.value.learningPath.findIndex(item => !item.completed)
})

// 刷新周数据
const refreshWeeklyData = async () => {
  try {
    const response = await analyticsAPI.getWeeklyActivity(userStore.user.id)
    analytics.value.weeklyActivity = response.data
    await nextTick()
    renderWeeklyChart()
    ElMessage.success('周数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

// 刷新月数据
const refreshMonthlyData = async () => {
  try {
    const response = await analyticsAPI.getMonthlyProgress(userStore.user.id)
    analytics.value.monthlyProgress = response.data
    await nextTick()
    renderMonthlyChart()
    ElMessage.success('月数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

// 生成报告
const generateReport = () => {
  ElMessage.success('完整报告生成中，请稍候...')
  setTimeout(() => {
    ElMessage.info('报告已生成，可在个人中心查看下载')
  }, 2000)
}

// 渲染周活动图表（简化版，实际项目中应使用ECharts等图表库）
const renderWeeklyChart = () => {
  if (!weeklyChartRef.value) return
  
  // 这里应该使用真正的图表库，比如ECharts
  // 现在用简单的HTML模拟
  const chartData = analytics.value.weeklyActivity
  const maxHours = Math.max(...chartData.map(d => d.hours))
  
  weeklyChartRef.value.innerHTML = `
    <div class="simple-chart">
      ${chartData.map(item => `
        <div class="chart-bar">
          <div class="bar" style="height: ${(item.hours / maxHours) * 100}%"></div>
          <div class="bar-label">${item.day}</div>
          <div class="bar-value">${item.hours}h</div>
        </div>
      `).join('')}
    </div>
  `
}

// 渲染月度图表
const renderMonthlyChart = () => {
  if (!monthlyChartRef.value) return
  
  const chartData = analytics.value.monthlyProgress
  const maxHours = Math.max(...chartData.map(d => d.studyHours))
  
  monthlyChartRef.value.innerHTML = `
    <div class="simple-chart">
      ${chartData.map(item => `
        <div class="chart-bar">
          <div class="bar monthly" style="height: ${(item.studyHours / maxHours) * 100}%"></div>
          <div class="bar-label">${item.month}</div>
          <div class="bar-value">${item.studyHours}h</div>
        </div>
      `).join('')}
    </div>
  `
}

// 渲染技能雷达图
const renderSkillChart = () => {
  if (!skillChartRef.value) return
  
  const skills = analytics.value.skillAssessment
  const skillNames = Object.keys(skills)
  
  skillChartRef.value.innerHTML = `
    <div class="skill-radar">
      <div class="radar-center">技能评估</div>
      ${skillNames.map((skill, index) => {
        const angle = (index * 360 / skillNames.length) - 90
        const level = skills[skill].level
        const progress = skills[skill].progress
        return `
          <div class="skill-item" style="transform: rotate(${angle}deg)">
            <div class="skill-line">
              <div class="skill-point current" style="left: ${level}%"></div>
              <div class="skill-point progress" style="left: ${Math.min(level + progress, 100)}%"></div>
            </div>
            <div class="skill-label" style="transform: rotate(${-angle}deg)">
              ${skill} (${level})
            </div>
          </div>
        `
      }).join('')}
    </div>
  `
}

// 获取分析数据
const fetchAnalytics = async () => {
  loading.value = true
  try {
    const response = await analyticsAPI.getLearningAnalytics(userStore.user.id)
    analytics.value = response.data
    
    await nextTick()
    renderWeeklyChart()
    renderMonthlyChart()
    renderSkillChart()
  } catch (error) {
    console.error('获取分析数据失败:', error)
    ElMessage.error('获取分析数据失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await fetchAnalytics()
})
</script>

<style scoped>
.analytics-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 16px;
  color: #606266;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-3px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-card.primary .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.info .stat-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
}

.stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 28px;
  color: #303133;
  font-weight: bold;
}

.stat-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.charts-section {
  margin-bottom: 40px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-card.full-width {
  grid-column: 1 / -1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  color: #303133;
}

.skill-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.current {
  background: #409eff;
}

.legend-color.progress {
  background: #67c23a;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container.large {
  height: 300px;
}

/* 简单图表样式 */
.simple-chart {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 100%;
  padding: 20px 0;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.bar {
  width: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px 4px 0 0;
  min-height: 10px;
  transition: all 0.3s;
}

.bar.monthly {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bar-label {
  font-size: 12px;
  color: #606266;
}

.bar-value {
  font-size: 12px;
  color: #303133;
  font-weight: bold;
}

/* 技能雷达图样式 */
.skill-radar {
  position: relative;
  width: 250px;
  height: 250px;
  margin: 0 auto;
}

.radar-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #409eff;
  color: white;
  padding: 10px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
}

.skill-item {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 2px;
  transform-origin: left center;
}

.skill-line {
  position: relative;
  width: 100%;
  height: 2px;
  background: #e4e7ed;
}

.skill-point {
  position: absolute;
  top: -4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transform: translateX(-50%);
}

.skill-point.current {
  background: #409eff;
}

.skill-point.progress {
  background: #67c23a;
}

.skill-label {
  position: absolute;
  top: -20px;
  left: 100%;
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  transform-origin: left center;
}

.learning-path-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 30px;
}

.section-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
}

.section-header p {
  margin: 0;
  color: #606266;
}

.path-timeline {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.path-item {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.path-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 1;
}

.path-item.completed .path-icon {
  background: #67c23a;
  color: white;
}

.path-item.current .path-icon {
  background: #409eff;
  color: white;
}

.path-item:not(.completed):not(.current) .path-icon {
  background: #e4e7ed;
  color: #909399;
}

.path-content {
  flex: 1;
}

.path-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.path-score {
  color: #67c23a;
  font-weight: bold;
}

.path-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.path-progress span {
  color: #606266;
  font-size: 14px;
  min-width: 80px;
}

.path-connector {
  position: absolute;
  left: 19px;
  top: 40px;
  width: 2px;
  height: 20px;
  background: #e4e7ed;
}

.report-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.report-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.report-card {
  display: flex;
  gap: 15px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s;
}

.report-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.report-icon {
  width: 40px;
  height: 40px;
  background: #f0f9ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  flex-shrink: 0;
}

.report-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.report-content p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 24px;
  }
  
  .overview-stats {
    grid-template-columns: 1fr;
  }
  
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .report-cards {
    grid-template-columns: 1fr;
  }
  
  .path-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .path-connector {
    display: none;
  }
}
</style>
