package com.div.education.controller;

import com.div.education.dto.ApiResponse;
import com.div.education.dto.PageResponse;
import com.div.education.entity.Course;
import com.div.education.repository.CourseRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课程控制器
 */
@RestController
@RequestMapping("/courses")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class CourseController {

    private final CourseRepository courseRepository;

    /**
     * 获取课程列表
     */
    @GetMapping
    public ApiResponse<PageResponse<Course>> getCourses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String level) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Course> coursePage;
        
        if (category != null && level != null) {
            coursePage = courseRepository.findByCategoryAndLevel(category, level, pageable);
        } else if (category != null) {
            coursePage = courseRepository.findByCategory(category, pageable);
        } else if (level != null) {
            coursePage = courseRepository.findByLevel(level, pageable);
        } else {
            coursePage = courseRepository.findByStatus(Course.CourseStatus.PUBLISHED, pageable);
        }
        
        return ApiResponse.success(PageResponse.of(coursePage));
    }

    /**
     * 根据ID获取课程
     */
    @GetMapping("/{id}")
    public ApiResponse<Course> getCourseById(@PathVariable Long id) {
        return courseRepository.findById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.notFound("课程不存在"));
    }

    /**
     * 搜索课程
     */
    @GetMapping("/search")
    public ApiResponse<PageResponse<Course>> searchCourses(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Course> coursePage = courseRepository.searchCourses(keyword, pageable);
        return ApiResponse.success(PageResponse.of(coursePage));
    }

    /**
     * 获取热门课程
     */
    @GetMapping("/popular")
    public ApiResponse<List<Course>> getPopularCourses(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Course> courses = courseRepository.findPopularCourses(pageable);
        return ApiResponse.success(courses);
    }

    /**
     * 获取最新课程
     */
    @GetMapping("/latest")
    public ApiResponse<List<Course>> getLatestCourses(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Course> courses = courseRepository.findLatestCourses(pageable);
        return ApiResponse.success(courses);
    }

    /**
     * 获取推荐课程
     */
    @GetMapping("/recommended")
    public ApiResponse<List<Course>> getRecommendedCourses(
            @RequestParam(defaultValue = "4.0") Double minRating,
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Course> courses = courseRepository.findRecommendedCourses(minRating, pageable);
        return ApiResponse.success(courses);
    }

    /**
     * 根据教师获取课程
     */
    @GetMapping("/teacher/{teacherId}")
    public ApiResponse<PageResponse<Course>> getCoursesByTeacher(
            @PathVariable Long teacherId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Course> coursePage = courseRepository.findByTeacherId(teacherId, pageable);
        return ApiResponse.success(PageResponse.of(coursePage));
    }

    /**
     * 创建课程
     */
    @PostMapping
    public ApiResponse<Course> createCourse(@RequestBody Course course) {
        try {
            course.setStatus(Course.CourseStatus.DRAFT);
            Course savedCourse = courseRepository.save(course);
            return ApiResponse.success("课程创建成功", savedCourse);
        } catch (Exception e) {
            return ApiResponse.error("课程创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新课程
     */
    @PutMapping("/{id}")
    public ApiResponse<Course> updateCourse(@PathVariable Long id, @RequestBody Course course) {
        try {
            if (!courseRepository.existsById(id)) {
                return ApiResponse.notFound("课程不存在");
            }
            course.setId(id);
            Course updatedCourse = courseRepository.save(course);
            return ApiResponse.success("课程更新成功", updatedCourse);
        } catch (Exception e) {
            return ApiResponse.error("课程更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除课程
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteCourse(@PathVariable Long id) {
        try {
            if (!courseRepository.existsById(id)) {
                return ApiResponse.notFound("课程不存在");
            }
            courseRepository.deleteById(id);
            return ApiResponse.success("课程删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error("课程删除失败: " + e.getMessage());
        }
    }
}
