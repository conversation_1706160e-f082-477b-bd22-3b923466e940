<template>
  <div class="course-detail-page">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>
      
      <div v-else-if="!course" class="error-container">
        <el-result
          icon="warning"
          title="课程不存在"
          sub-title="抱歉，您访问的课程不存在或已被删除"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/learning')">
              返回学习中心
            </el-button>
          </template>
        </el-result>
      </div>
      
      <div v-else class="course-content">
        <!-- 课程头部信息 -->
        <div class="course-header">
          <div class="course-cover">
            <img :src="course.coverImage" :alt="course.title" />
            <div class="play-overlay">
              <el-button type="primary" size="large" circle>
                <el-icon size="24"><VideoPlay /></el-icon>
              </el-button>
            </div>
          </div>
          
          <div class="course-info">
            <div class="course-category">{{ course.categoryName }}</div>
            <h1 class="course-title">{{ course.title }}</h1>
            <p class="course-description">{{ course.description }}</p>
            
            <!-- 课程统计 -->
            <div class="course-stats">
              <div class="stat-item">
                <el-icon><Star /></el-icon>
                <span>{{ course.rating }} 评分</span>
              </div>
              <div class="stat-item">
                <el-icon><User /></el-icon>
                <span>{{ course.studentsCount }} 学员</span>
              </div>
              <div class="stat-item">
                <el-icon><Clock /></el-icon>
                <span>{{ formatDuration(course.duration) }}</span>
              </div>
              <div class="stat-item">
                <el-icon><Trophy /></el-icon>
                <span>{{ course.difficulty === 'beginner' ? '初级' : course.difficulty === 'intermediate' ? '中级' : '高级' }}</span>
              </div>
            </div>
            
            <!-- 讲师信息 -->
            <div class="instructor-info">
              <el-avatar :src="course.instructor.avatar" :size="40" />
              <div class="instructor-details">
                <h4>{{ course.instructor.name }}</h4>
                <span>课程讲师</span>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="course-actions">
              <el-button type="primary" size="large" @click="startLearning">
                <el-icon><VideoPlay /></el-icon>
                开始学习
              </el-button>
              <el-button size="large" @click="toggleFavorite">
                <el-icon><Star /></el-icon>
                收藏课程
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 课程详情标签页 -->
        <div class="course-tabs">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="课程目录" name="chapters">
              <div class="chapters-list">
                <div
                  v-for="(chapter, index) in course.chapters"
                  :key="chapter.id"
                  class="chapter-item"
                  @click="playChapter(chapter)"
                >
                  <div class="chapter-number">{{ index + 1 }}</div>
                  <div class="chapter-info">
                    <h4>{{ chapter.title }}</h4>
                    <span class="chapter-duration">{{ formatDuration(chapter.duration) }}</span>
                  </div>
                  <div class="chapter-status">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="课程介绍" name="description">
              <div class="course-description-detail">
                <h3>课程简介</h3>
                <p>{{ course.description }}</p>
                
                <h3>学习目标</h3>
                <ul>
                  <li>掌握{{ course.title }}的核心概念和基础知识</li>
                  <li>能够独立完成相关项目开发</li>
                  <li>具备解决实际问题的能力</li>
                  <li>为进阶学习打下坚实基础</li>
                </ul>
                
                <h3>适合人群</h3>
                <ul>
                  <li>对{{ course.categoryName }}感兴趣的初学者</li>
                  <li>希望系统学习相关技能的开发者</li>
                  <li>需要提升专业技能的在职人员</li>
                </ul>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="学员评价" name="reviews">
              <div class="reviews-section">
                <div class="reviews-summary">
                  <div class="rating-overview">
                    <div class="rating-score">{{ course.rating }}</div>
                    <div class="rating-stars">
                      <el-rate v-model="course.rating" disabled show-score />
                    </div>
                    <div class="rating-count">基于 {{ course.studentsCount }} 位学员的评价</div>
                  </div>
                </div>
                
                <div class="reviews-list">
                  <div class="review-item">
                    <div class="reviewer-info">
                      <el-avatar src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
                      <div class="reviewer-details">
                        <h5>学员A</h5>
                        <el-rate :model-value="5" disabled size="small" />
                      </div>
                    </div>
                    <p class="review-content">课程内容很棒，讲解清晰，实例丰富，非常适合初学者学习。</p>
                    <div class="review-time">2024-08-20</div>
                  </div>
                  
                  <div class="review-item">
                    <div class="reviewer-info">
                      <el-avatar src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
                      <div class="reviewer-details">
                        <h5>学员B</h5>
                        <el-rate :model-value="4" disabled size="small" />
                      </div>
                    </div>
                    <p class="review-content">老师讲得很好，项目实战部分特别有用，推荐！</p>
                    <div class="review-time">2024-08-18</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCourseStore } from '../stores/course.js'
import { useUserStore } from '../stores/user.js'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const courseStore = useCourseStore()
const userStore = useUserStore()

const loading = ref(true)
const course = ref(null)
const activeTab = ref('chapters')

// 格式化时长
const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

// 开始学习
const startLearning = () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  ElMessage.success('开始学习课程')
  // 这里可以跳转到视频播放页面
}

// 切换收藏状态
const toggleFavorite = () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  ElMessage.success('已收藏课程')
}

// 播放章节
const playChapter = (chapter) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  ElMessage.info(`播放: ${chapter.title}`)
  // 这里可以实现视频播放逻辑
}

// 获取课程详情
const fetchCourseDetail = async () => {
  const courseId = parseInt(route.params.id)
  
  try {
    loading.value = true
    await courseStore.fetchCourseDetail(courseId)
    course.value = courseStore.currentCourse
  } catch (error) {
    console.error('获取课程详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await fetchCourseDetail()
})
</script>

<style scoped>
.course-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.loading-container,
.error-container {
  padding: 40px 0;
}

.course-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 30px;
}

.course-cover {
  position: relative;
  width: 400px;
  height: 225px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.course-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.course-info {
  flex: 1;
}

.course-category {
  display: inline-block;
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 15px;
}

.course-title {
  font-size: 28px;
  color: #303133;
  margin-bottom: 15px;
  line-height: 1.3;
}

.course-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 20px;
}

.course-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 25px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 14px;
}

.instructor-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.instructor-details h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.instructor-details span {
  color: #909399;
  font-size: 14px;
}

.course-actions {
  display: flex;
  gap: 15px;
}

.course-tabs {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chapter-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.chapter-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.chapter-number {
  width: 30px;
  height: 30px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.chapter-info {
  flex: 1;
}

.chapter-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.chapter-duration {
  color: #909399;
  font-size: 14px;
}

.chapter-status {
  color: #409eff;
}

.course-description-detail h3 {
  color: #303133;
  margin: 20px 0 15px 0;
}

.course-description-detail ul {
  padding-left: 20px;
  line-height: 1.8;
}

.course-description-detail li {
  color: #606266;
  margin-bottom: 8px;
}

.reviews-section {
  max-width: 800px;
}

.reviews-summary {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.rating-overview {
  display: flex;
  align-items: center;
  gap: 20px;
}

.rating-score {
  font-size: 48px;
  font-weight: bold;
  color: #409eff;
}

.rating-count {
  color: #909399;
  font-size: 14px;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-item {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.reviewer-details h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.review-content {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 10px;
}

.review-time {
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .course-cover {
    width: 100%;
    height: 200px;
  }
  
  .course-title {
    font-size: 24px;
  }
  
  .course-stats {
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .course-actions {
    flex-direction: column;
  }
  
  .rating-overview {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}
</style>
