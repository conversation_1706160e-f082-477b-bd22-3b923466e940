<template>
  <div class="course-card" @click="$emit('click')">
    <!-- 课程封面 -->
    <div class="course-cover">
      <img :src="course.coverImage" :alt="course.title" />
      <div class="course-overlay">
        <el-icon class="play-icon" size="40">
          <VideoPlay />
        </el-icon>
      </div>
      <!-- 难度标签 -->
      <div class="difficulty-badge" :class="difficultyClass">
        {{ difficultyText }}
      </div>
      <!-- 免费标签 -->
      <div v-if="course.price === 0" class="free-badge">
        免费
      </div>
    </div>

    <!-- 课程信息 -->
    <div class="course-info">
      <h3 class="course-title">{{ course.title }}</h3>
      <p class="course-description">{{ course.description }}</p>
      
      <!-- 讲师信息 -->
      <div class="instructor-info">
        <el-avatar :src="course.instructor.avatar" :size="24" />
        <span class="instructor-name">{{ course.instructor.name }}</span>
      </div>

      <!-- 课程统计 -->
      <div class="course-stats">
        <div class="stat-item">
          <el-icon><Star /></el-icon>
          <span>{{ course.rating }}</span>
        </div>
        <div class="stat-item">
          <el-icon><User /></el-icon>
          <span>{{ formatNumber(course.studentsCount) }}</span>
        </div>
        <div class="stat-item">
          <el-icon><Clock /></el-icon>
          <span>{{ formatDuration(course.duration) }}</span>
        </div>
      </div>

      <!-- 课程标签 -->
      <div class="course-tags">
        <el-tag 
          v-for="tag in course.tags" 
          :key="tag" 
          size="small" 
          type="info"
        >
          {{ tag }}
        </el-tag>
      </div>

      <!-- 学习进度（如果用户已登录且有学习记录） -->
      <div v-if="progress !== null" class="learning-progress">
        <div class="progress-text">
          <span>学习进度</span>
          <span>{{ progress }}%</span>
        </div>
        <el-progress :percentage="progress" :show-text="false" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useUserStore } from '../../stores/user.js'

const props = defineProps({
  course: {
    type: Object,
    required: true
  },
  progress: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['click'])

const userStore = useUserStore()

// 难度等级样式
const difficultyClass = computed(() => {
  const classMap = {
    'beginner': 'difficulty-beginner',
    'intermediate': 'difficulty-intermediate',
    'advanced': 'difficulty-advanced'
  }
  return classMap[props.course.difficulty] || 'difficulty-beginner'
})

// 难度等级文本
const difficultyText = computed(() => {
  const textMap = {
    'beginner': '初级',
    'intermediate': '中级',
    'advanced': '高级'
  }
  return textMap[props.course.difficulty] || '初级'
})

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 格式化时长
const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`
  }
  return `${minutes}m`
}
</script>

<style scoped>
.course-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(228, 231, 237, 0.3);
  position: relative;
}

.course-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.course-card:hover::before {
  transform: scaleX(1);
}

.course-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.2);
}

.course-cover {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.course-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
  filter: brightness(0.9);
}

.course-card:hover .course-cover img {
  transform: scale(1.08);
  filter: brightness(1);
}

.course-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s ease;
  backdrop-filter: blur(2px);
}

.course-card:hover .course-overlay {
  opacity: 1;
}

.play-icon {
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.difficulty-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.difficulty-beginner {
  background: #67c23a;
}

.difficulty-intermediate {
  background: #e6a23c;
}

.difficulty-advanced {
  background: #f56c6c;
}

.free-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.course-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.instructor-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.instructor-name {
  font-size: 14px;
  color: #606266;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #909399;
}

.stat-item .el-icon {
  font-size: 16px;
}

.course-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 15px;
}

.learning-progress {
  margin-top: auto;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-cover {
    height: 160px;
  }
  
  .course-info {
    padding: 15px;
  }
  
  .course-title {
    font-size: 16px;
  }
  
  .course-stats {
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
