<template>
  <div class="study-groups-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>学习小组</h1>
        <p>加入学习小组，与志同道合的伙伴一起学习</p>
        <el-button 
          v-if="userStore.isLoggedIn" 
          type="primary" 
          size="large"
          @click="$router.push('/groups/create')"
        >
          <el-icon><Plus /></el-icon>
          创建小组
        </el-button>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索学习小组..."
          size="large"
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>

      <!-- 小组列表 -->
      <div class="groups-section">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="4" animated />
        </div>
        
        <div v-else-if="groupsList.length === 0" class="empty-container">
          <el-empty description="暂无学习小组" />
        </div>
        
        <div v-else class="groups-grid">
          <div
            v-for="group in groupsList"
            :key="group.id"
            class="group-card"
            @click="goToGroup(group.id)"
          >
            <!-- 小组封面 -->
            <div class="group-cover">
              <img :src="group.coverImage" :alt="group.name" />
              <div class="group-overlay">
                <el-icon class="group-icon" size="40">
                  <UserFilled />
                </el-icon>
              </div>
            </div>

            <!-- 小组信息 -->
            <div class="group-info">
              <h3 class="group-name">{{ group.name }}</h3>
              <p class="group-description">{{ group.description }}</p>
              
              <!-- 创建者信息 -->
              <div class="creator-info">
                <el-avatar :src="group.creator.avatar" :size="24" />
                <span class="creator-name">{{ group.creator.username }}</span>
              </div>

              <!-- 小组统计 -->
              <div class="group-stats">
                <div class="stat-item">
                  <el-icon><User /></el-icon>
                  <span>{{ group.memberCount }}/{{ group.maxMembers }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatDate(group.createdAt) }}</span>
                </div>
              </div>

              <!-- 小组标签 -->
              <div class="group-tags">
                <el-tag 
                  v-for="tag in group.tags" 
                  :key="tag" 
                  size="small" 
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <!-- 最近活动 -->
              <div class="recent-activity">
                <el-icon><Clock /></el-icon>
                <span>{{ group.recentActivity }}</span>
              </div>

              <!-- 加入按钮 -->
              <div class="group-actions">
                <el-button 
                  type="primary" 
                  size="small"
                  :disabled="group.memberCount >= group.maxMembers"
                  @click.stop="handleJoinGroup(group)"
                >
                  {{ group.memberCount >= group.maxMembers ? '已满员' : '加入小组' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { groupAPI } from '../mock/api.js'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const groupsList = ref([])
const searchKeyword = ref('')

// 处理搜索
const handleSearch = () => {
  fetchGroups()
}

// 跳转到小组详情
const goToGroup = (groupId) => {
  router.push(`/groups/${groupId}`)
}

// 处理加入小组
const handleJoinGroup = async (group) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    // 这里应该调用加入小组的API
    ElMessage.success('加入小组成功')
    // 更新本地数据
    group.memberCount += 1
  } catch (error) {
    ElMessage.error('加入小组失败')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// 获取小组列表
const fetchGroups = async () => {
  loading.value = true
  try {
    const params = {}
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    
    const response = await groupAPI.getGroups(params)
    groupsList.value = response.data
  } catch (error) {
    console.error('获取学习小组失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await fetchGroups()
})
</script>

<style scoped>
.study-groups-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
}

.search-input {
  max-width: 600px;
}

.groups-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container,
.empty-container {
  padding: 40px 0;
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.group-card {
  border: 1px solid #ebeef5;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.group-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.group-cover {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.group-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.group-card:hover .group-cover img {
  transform: scale(1.05);
}

.group-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.group-card:hover .group-overlay {
  opacity: 1;
}

.group-icon {
  color: white;
}

.group-info {
  padding: 20px;
}

.group-name {
  font-size: 18px;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.group-description {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.creator-name {
  font-size: 14px;
  color: #606266;
}

.group-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #909399;
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 15px;
}

.recent-activity {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 15px;
}

.group-actions {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 24px;
  }
  
  .groups-section {
    padding: 20px;
  }
  
  .groups-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .group-cover {
    height: 140px;
  }
  
  .group-info {
    padding: 15px;
  }
}
</style>
