<template>
  <div class="learning-center">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>学习中心</h1>
        <p>发现优质课程，开启学习之旅</p>
      </div>

      <!-- 搜索和筛选区域 -->
      <div class="search-filter-section">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索课程..."
            size="large"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>

        <div class="filter-bar">
          <!-- 分类筛选 -->
          <div class="filter-item">
            <label>分类：</label>
            <el-select
              v-model="selectedCategory"
              placeholder="选择分类"
              clearable
              @change="handleCategoryChange"
            >
              <el-option
                v-for="category in courseStore.categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </div>

          <!-- 难度筛选 -->
          <div class="filter-item">
            <label>难度：</label>
            <el-select
              v-model="selectedDifficulty"
              placeholder="选择难度"
              clearable
              @change="handleDifficultyChange"
            >
              <el-option label="初级" value="beginner" />
              <el-option label="中级" value="intermediate" />
              <el-option label="高级" value="advanced" />
            </el-select>
          </div>

          <!-- 排序方式 -->
          <div class="filter-item">
            <label>排序：</label>
            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              @change="handleSortChange"
            >
              <el-option label="最新发布" value="latest" />
              <el-option label="最受欢迎" value="popular" />
              <el-option label="评分最高" value="rating" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 课程列表 -->
      <div class="courses-section">
        <div v-if="courseStore.loading" class="loading-container">
          <el-skeleton :rows="6" animated />
        </div>
        
        <div v-else-if="courseStore.courses.length === 0" class="empty-container">
          <el-empty description="暂无课程数据" />
        </div>
        
        <div v-else class="courses-grid">
          <CourseCard
            v-for="course in courseStore.courses"
            :key="course.id"
            :course="course"
            :progress="getUserProgress(course.id)"
            @click="goToCourse(course.id)"
          />
        </div>

        <!-- 分页 -->
        <div v-if="courseStore.courses.length > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="courseStore.pagination.total"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCourseStore } from '../stores/course.js'
import { useUserStore } from '../stores/user.js'
import CourseCard from '../components/Course/CourseCard.vue'

const route = useRoute()
const router = useRouter()
const courseStore = useCourseStore()
const userStore = useUserStore()

// 搜索和筛选状态
const searchKeyword = ref('')
const selectedCategory = ref(null)
const selectedDifficulty = ref('')
const sortBy = ref('latest')

// 分页状态
const currentPage = ref(1)
const pageSize = ref(12)

// 获取用户学习进度
const getUserProgress = (courseId) => {
  if (!userStore.isLoggedIn) return null
  const key = `${userStore.user.id}-${courseId}`
  return courseStore.learningProgress[key]?.progress || null
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchCourses()
}

// 处理分类变化
const handleCategoryChange = () => {
  currentPage.value = 1
  fetchCourses()
}

// 处理难度变化
const handleDifficultyChange = () => {
  currentPage.value = 1
  fetchCourses()
}

// 处理排序变化
const handleSortChange = () => {
  currentPage.value = 1
  fetchCourses()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchCourses()
}

// 处理页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchCourses()
}

// 跳转到课程详情
const goToCourse = (courseId) => {
  router.push(`/learning/course/${courseId}`)
}

// 获取课程列表
const fetchCourses = async () => {
  const params = {
    page: currentPage.value,
    pageSize: pageSize.value
  }

  if (searchKeyword.value) {
    params.keyword = searchKeyword.value
  }

  if (selectedCategory.value) {
    params.categoryId = selectedCategory.value
  }

  if (selectedDifficulty.value) {
    params.difficulty = selectedDifficulty.value
  }

  if (sortBy.value) {
    params.sortBy = sortBy.value
  }

  await courseStore.fetchCourses(params)
}

// 页面初始化
onMounted(async () => {
  // 从URL参数获取初始筛选条件
  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }
  if (route.query.categoryId) {
    selectedCategory.value = parseInt(route.query.categoryId)
  }

  // 获取分类列表
  await courseStore.fetchCategories()
  
  // 获取课程列表
  await fetchCourses()

  // 如果用户已登录，获取学习记录
  if (userStore.isLoggedIn) {
    await courseStore.fetchUserLearningRecords(userStore.user.id)
  }
})
</script>

<style scoped>
.learning-center {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 16px;
  color: #606266;
}

.search-filter-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-bar {
  margin-bottom: 20px;
}

.search-input {
  max-width: 600px;
}

.filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.courses-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container,
.empty-container {
  padding: 40px 0;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 24px;
  }
  
  .search-filter-section {
    padding: 20px;
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .courses-section {
    padding: 20px;
  }
}
</style>
