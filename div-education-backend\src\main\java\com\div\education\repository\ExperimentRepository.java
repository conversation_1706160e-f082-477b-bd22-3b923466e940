package com.div.education.repository;

import com.div.education.entity.Experiment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 实验数据访问层
 */
@Repository
public interface ExperimentRepository extends JpaRepository<Experiment, Long> {

    /**
     * 根据学科查找实验
     */
    Page<Experiment> findBySubject(String subject, Pageable pageable);

    /**
     * 根据难度查找实验
     */
    Page<Experiment> findByDifficulty(Experiment.ExperimentDifficulty difficulty, Pageable pageable);

    /**
     * 根据状态查找实验
     */
    Page<Experiment> findByStatus(Experiment.ExperimentStatus status, Pageable pageable);

    /**
     * 根据学科和难度查找实验
     */
    Page<Experiment> findBySubjectAndDifficulty(String subject, Experiment.ExperimentDifficulty difficulty, Pageable pageable);

    /**
     * 根据关键词搜索实验
     */
    @Query("SELECT e FROM Experiment e WHERE e.title LIKE %:keyword% OR e.description LIKE %:keyword%")
    Page<Experiment> searchExperiments(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取热门实验（按完成人数排序）
     */
    @Query("SELECT e FROM Experiment e WHERE e.status = 'ACTIVE' ORDER BY e.completedCount DESC")
    List<Experiment> findPopularExperiments(Pageable pageable);

    /**
     * 获取最新实验
     */
    @Query("SELECT e FROM Experiment e WHERE e.status = 'ACTIVE' ORDER BY e.createdAt DESC")
    List<Experiment> findLatestExperiments(Pageable pageable);

    /**
     * 根据学科统计实验数量
     */
    @Query("SELECT e.subject, COUNT(e) FROM Experiment e WHERE e.status = 'ACTIVE' GROUP BY e.subject")
    List<Object[]> countExperimentsBySubject();
}
