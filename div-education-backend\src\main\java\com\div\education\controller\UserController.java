package com.div.education.controller;

import com.div.education.dto.ApiResponse;
import com.div.education.dto.PageResponse;
import com.div.education.dto.UserDTO;
import com.div.education.entity.User;
import com.div.education.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class UserController {

    private final UserService userService;

    /**
     * 获取用户列表
     */
    @GetMapping
    public ApiResponse<PageResponse<UserDTO>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        PageResponse<UserDTO> users = userService.getUsers(pageable);
        return ApiResponse.success(users);
    }

    /**
     * 根据ID获取用户
     */
    @GetMapping("/{id}")
    public ApiResponse<UserDTO> getUserById(@PathVariable Long id) {
        return userService.findById(id)
                .map(user -> ApiResponse.success(UserDTO.fromEntity(user)))
                .orElse(ApiResponse.notFound("用户不存在"));
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    public ApiResponse<PageResponse<UserDTO>> searchUsers(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        PageResponse<UserDTO> users = userService.searchUsers(keyword, pageable);
        return ApiResponse.success(users);
    }

    /**
     * 根据角色获取用户
     */
    @GetMapping("/role/{role}")
    public ApiResponse<PageResponse<UserDTO>> getUsersByRole(
            @PathVariable User.UserRole role,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        PageResponse<UserDTO> users = userService.getUsersByRole(role, pageable);
        return ApiResponse.success(users);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public ApiResponse<UserDTO> updateUser(
            @PathVariable Long id,
            @RequestBody UpdateUserRequest request) {
        
        try {
            User updatedUser = userService.updateUser(
                    id, 
                    request.getNickname(), 
                    request.getAvatar(), 
                    request.getPhone(), 
                    request.getBio()
            );
            return ApiResponse.success(UserDTO.fromEntity(updatedUser));
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ApiResponse.success("用户删除成功", null);
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 用户更新请求DTO
     */
    public static class UpdateUserRequest {
        private String nickname;
        private String avatar;
        private String phone;
        private String bio;

        // Getters and Setters
        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }
        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getBio() { return bio; }
        public void setBio(String bio) { this.bio = bio; }
    }
}
