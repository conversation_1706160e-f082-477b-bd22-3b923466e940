-- DIV教育学习平台 - 示例数据脚本
-- 注意：密码已使用BCrypt加密，明文密码为123456

-- 插入用户数据
INSERT INTO users (username, email, password, nickname, role, status, bio) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '系统管理员', 'ADMIN', 'ACTIVE', '系统管理员账号'),
('teacher1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '李老师', 'TEACHER', 'ACTIVE', '前端开发讲师，拥有10年开发经验'),
('teacher2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '王老师', 'TEACHER', 'ACTIVE', '后端开发专家，Java技术栈资深讲师'),
('student1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '张同学', 'STUDENT', 'ACTIVE', '计算机科学专业学生'),
('student2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b2.lUKbYyTRZSu', '李同学', 'STUDENT', 'ACTIVE', '软件工程专业学生');

-- 插入课程数据
INSERT INTO courses (title, description, cover_image, instructor, category, level, duration, price, original_price, rating, student_count, lesson_count, status, tags, teacher_id) VALUES
('Vue.js 3.0 完整开发教程', '从零开始学习Vue.js 3.0，包含组合式API、响应式系统、路由管理等核心概念', 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400', '李老师', '前端开发', 'INTERMEDIATE', 1200, 199.00, 299.00, 4.8, 1234, 24, 'PUBLISHED', 'Vue.js,前端,JavaScript,组件化', 2),
('React 现代开发实践', '学习React最新特性，包含Hooks、Context、性能优化等', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '李老师', '前端开发', 'INTERMEDIATE', 1500, 249.00, 349.00, 4.7, 967, 30, 'PUBLISHED', 'React,Hooks,前端,JavaScript', 2),
('Spring Boot 微服务实战', '深入学习Spring Boot框架，构建企业级微服务应用', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', '王老师', '后端开发', 'ADVANCED', 1800, 299.00, 399.00, 4.9, 856, 36, 'PUBLISHED', 'Spring Boot,微服务,Java,后端', 3);

-- 插入实验数据
INSERT INTO experiments (title, description, cover_image, subject, difficulty, duration, rating, completed_count, objectives, equipment, steps, tags, status) VALUES
('单摆运动实验', '通过改变摆长和初始角度，观察单摆的运动规律，验证单摆周期公式', 'https://images.unsplash.com/photo-1636466497217-26a8cbeaf0aa?w=400', '物理', 'BEGINNER', 30, 4.7, 1234, '理解单摆运动规律,验证周期公式,掌握实验操作方法', '摆球,细线,支架,量角器,秒表', '设置摆长,调整初始角度,释放摆球,测量周期,记录数据,分析结果', '物理,力学,周期运动', 'ACTIVE'),
('酸碱中和滴定实验', '使用标准NaOH溶液滴定未知浓度的HCl溶液，学习滴定操作和终点判断', 'https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?w=400', '化学', 'INTERMEDIATE', 45, 4.8, 987, '掌握滴定操作技能,学习终点判断方法,计算未知溶液浓度', '滴定管,锥形瓶,NaOH标准溶液,HCl待测溶液,酚酞指示剂', '准备溶液,装填滴定管,加入指示剂,开始滴定,观察颜色变化,记录消耗体积', '化学,分析化学,滴定', 'ACTIVE');

-- 插入帖子数据
INSERT INTO posts (title, content, category, tags, view_count, like_count, comment_count, is_pinned, status, author_id) VALUES
('Vue 3.0 学习心得分享', '最近在学习Vue 3.0，感觉组合式API真的很强大，特别是在逻辑复用方面。想和大家分享一些学习心得...', '学习交流', 'Vue.js,前端,学习心得', 1234, 89, 23, true, 'PUBLISHED', 4),
('Spring Boot 项目实战经验', '在做毕业设计的过程中，使用Spring Boot构建了一个完整的后端系统，遇到了一些问题，也积累了一些经验...', '技术讨论', 'Spring Boot,后端,项目实战', 987, 67, 18, false, 'PUBLISHED', 5);

-- 插入学习小组数据
INSERT INTO study_groups (name, description, category, max_members, current_members, is_public, tags, status, creator_id) VALUES
('前端技术交流群', '专注于前端技术讨论和学习，欢迎Vue、React、Angular爱好者加入', '前端开发', 100, 45, true, '前端,Vue,React,技术交流', 'ACTIVE', 4),
('Java后端开发小组', 'Java后端技术学习小组，分享Spring Boot、微服务等技术经验', '后端开发', 80, 32, true, 'Java,Spring Boot,后端,微服务', 'ACTIVE', 5),
('算法与数据结构', '一起刷题，一起进步！专注算法和数据结构的学习讨论', '算法', 150, 78, true, '算法,数据结构,刷题,面试', 'ACTIVE', 4);

-- 插入新闻数据
INSERT INTO news (title, summary, content, cover_image, category, tags, source, author, view_count, like_count, is_hot, status, published_at) VALUES
('Vue 3.4 正式发布，带来重大性能提升', 'Vue.js 团队发布了 3.4 版本，在编译器优化、响应式系统等方面带来显著改进', 'Vue.js 3.4 版本正式发布，这个版本在多个方面带来了重大改进...', 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400', '前端技术', 'Vue.js,前端,更新', 'Vue官方', 'Vue团队', 2345, 156, true, 'PUBLISHED', '2024-01-15 10:00:00'),
('Spring Boot 3.2 发布，支持虚拟线程', 'Spring Boot 3.2 版本发布，正式支持 Java 21 的虚拟线程特性，大幅提升并发性能', 'Spring Boot 3.2 版本带来了对 Java 21 虚拟线程的完整支持...', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', '后端技术', 'Spring Boot,Java,虚拟线程', 'Spring官方', 'Spring团队', 1876, 98, true, 'PUBLISHED', '2024-01-10 14:30:00'),
('2024年前端开发趋势预测', '分析2024年前端开发的主要趋势，包括框架发展、工具链演进等', '2024年前端开发将继续快速发展，本文分析了几个主要趋势...', 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400', '行业动态', '前端,趋势,2024', 'TechNews', '技术编辑', 1234, 67, false, 'PUBLISHED', '2024-01-08 09:15:00');

-- 插入学习记录数据
INSERT INTO learning_records (user_id, course_id, progress, last_position) VALUES
(4, 1, 75, 18),  -- 张同学学习Vue课程，进度75%
(4, 2, 30, 9),   -- 张同学学习React课程，进度30%
(5, 3, 90, 32),  -- 李同学学习Spring Boot课程，进度90%
(5, 1, 45, 11);  -- 李同学学习Vue课程，进度45%
