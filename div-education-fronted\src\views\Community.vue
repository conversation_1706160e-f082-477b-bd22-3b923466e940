<template>
  <div class="community-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>交流社区</h1>
        <p>分享经验，交流学习，共同成长</p>
        <el-button 
          v-if="userStore.isLoggedIn" 
          type="primary" 
          size="large"
          @click="$router.push('/community/create')"
        >
          <el-icon><Edit /></el-icon>
          发布帖子
        </el-button>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索帖子..."
          size="large"
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>

      <!-- 帖子列表 -->
      <div class="posts-section">
        <div v-if="communityStore.loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        
        <div v-else-if="communityStore.posts.length === 0" class="empty-container">
          <el-empty description="暂无帖子数据" />
        </div>
        
        <div v-else class="posts-list">
          <div
            v-for="post in communityStore.posts"
            :key="post.id"
            class="post-card"
            @click="goToPost(post.id)"
          >
            <!-- 帖子头部 -->
            <div class="post-header">
              <div class="author-info">
                <el-avatar :src="post.author.avatar" :size="40" />
                <div class="author-details">
                  <h4>{{ post.author.username }}</h4>
                  <span class="author-level">{{ post.author.level }}</span>
                </div>
              </div>
              <div class="post-time">
                {{ formatTime(post.createdAt) }}
              </div>
            </div>

            <!-- 帖子内容 -->
            <div class="post-content">
              <h3 class="post-title">{{ post.title }}</h3>
              <p class="post-summary">{{ post.content }}</p>
              
              <!-- 帖子图片 -->
              <div v-if="post.images && post.images.length > 0" class="post-images">
                <img
                  v-for="(image, index) in post.images.slice(0, 3)"
                  :key="index"
                  :src="image"
                  :alt="`图片${index + 1}`"
                  class="post-image"
                />
              </div>

              <!-- 帖子标签 -->
              <div class="post-tags">
                <el-tag
                  v-for="tag in post.tags"
                  :key="tag"
                  size="small"
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <!-- 帖子统计 -->
            <div class="post-stats">
              <div class="stat-item" @click.stop="handleLike(post)">
                <el-icon :class="{ liked: post.isLiked }">
                  <component :is="post.isLiked ? 'StarFilled' : 'Star'" />
                </el-icon>
                <span>{{ post.likesCount }}</span>
              </div>
              <div class="stat-item">
                <el-icon><ChatDotRound /></el-icon>
                <span>{{ post.commentsCount }}</span>
              </div>
              <div class="stat-item">
                <el-icon><View /></el-icon>
                <span>{{ post.viewsCount }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="communityStore.posts.length > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="communityStore.pagination.total"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCommunityStore } from '../stores/community.js'
import { useUserStore } from '../stores/user.js'
import { ElMessage } from 'element-plus'

const router = useRouter()
const communityStore = useCommunityStore()
const userStore = useUserStore()

// 搜索状态
const searchKeyword = ref('')

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchPosts()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchPosts()
}

// 处理页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchPosts()
}

// 跳转到帖子详情
const goToPost = (postId) => {
  router.push(`/community/post/${postId}`)
}

// 处理点赞
const handleLike = async (post) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    await communityStore.likePost(post.id)
    ElMessage.success(post.isLiked ? '点赞成功' : '取消点赞')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 格式化时间
const formatTime = (timeString) => {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now - time
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

// 获取帖子列表
const fetchPosts = async () => {
  const params = {
    page: currentPage.value,
    pageSize: pageSize.value
  }

  if (searchKeyword.value) {
    params.keyword = searchKeyword.value
  }

  await communityStore.fetchPosts(params)
}

// 页面初始化
onMounted(async () => {
  await fetchPosts()
})
</script>

<style scoped>
.community-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 30px;
}

.search-input {
  max-width: 600px;
}

.posts-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container,
.empty-container {
  padding: 40px 0;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.post-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.post-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.author-level {
  font-size: 12px;
  color: #909399;
}

.post-time {
  font-size: 14px;
  color: #909399;
}

.post-content {
  margin-bottom: 15px;
}

.post-title {
  font-size: 18px;
  color: #303133;
  margin-bottom: 10px;
  line-height: 1.4;
}

.post-summary {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-images {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.post-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.post-stats {
  display: flex;
  gap: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f2f5;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #909399;
  cursor: pointer;
  transition: color 0.3s;
}

.stat-item:hover {
  color: #409eff;
}

.stat-item.liked,
.stat-item .liked {
  color: #f56c6c;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .post-card {
    padding: 15px;
  }
  
  .post-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .post-images {
    flex-wrap: wrap;
  }
  
  .post-image {
    width: 60px;
    height: 60px;
  }
}
</style>
