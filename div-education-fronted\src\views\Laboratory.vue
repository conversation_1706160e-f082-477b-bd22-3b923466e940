<template>
  <div class="laboratory-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>仿真实验室</h1>
        <p>在虚拟环境中进行物理化学实验，安全便捷地探索科学奥秘</p>
      </div>

      <!-- 实验统计 -->
      <div class="lab-stats">
        <div class="stat-card">
          <div class="stat-icon physics">
            <el-icon size="32"><Lightning /></el-icon>
          </div>
          <div class="stat-info">
            <h3>物理实验</h3>
            <p>{{ physicsCount }} 个实验</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon chemistry">
            <el-icon size="32"><MagicStick /></el-icon>
          </div>
          <div class="stat-info">
            <h3>化学实验</h3>
            <p>{{ chemistryCount }} 个实验</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon completed">
            <el-icon size="32"><Trophy /></el-icon>
          </div>
          <div class="stat-info">
            <h3>已完成</h3>
            <p>{{ completedCount }} 个实验</p>
          </div>
        </div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="filter-bar">
          <div class="filter-item">
            <label>学科：</label>
            <el-select
              v-model="selectedSubject"
              placeholder="选择学科"
              clearable
              @change="handleSubjectChange"
            >
              <el-option label="物理" value="物理" />
              <el-option label="化学" value="化学" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>难度：</label>
            <el-select
              v-model="selectedDifficulty"
              placeholder="选择难度"
              clearable
              @change="handleDifficultyChange"
            >
              <el-option label="初级" value="beginner" />
              <el-option label="中级" value="intermediate" />
              <el-option label="高级" value="advanced" />
            </el-select>
          </div>

          <div class="filter-item">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索实验..."
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </div>

      <!-- 实验列表 -->
      <div class="experiments-section">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="4" animated />
        </div>
        
        <div v-else-if="experiments.length === 0" class="empty-container">
          <el-empty description="暂无实验数据" />
        </div>
        
        <div v-else class="experiments-grid">
          <div
            v-for="experiment in experiments"
            :key="experiment.id"
            class="experiment-card"
            @click="goToExperiment(experiment.id)"
          >
            <!-- 实验封面 -->
            <div class="experiment-cover">
              <img :src="experiment.coverImage" :alt="experiment.title" />
              <div class="experiment-overlay">
                <el-icon class="play-icon" size="40">
                  <VideoPlay />
                </el-icon>
              </div>
              <!-- 学科标签 -->
              <div class="subject-badge" :class="experiment.subject.toLowerCase()">
                {{ experiment.subject }}
              </div>
              <!-- 难度标签 -->
              <div class="difficulty-badge" :class="getDifficultyClass(experiment.difficulty)">
                {{ getDifficultyText(experiment.difficulty) }}
              </div>
            </div>

            <!-- 实验信息 -->
            <div class="experiment-info">
              <h3 class="experiment-title">{{ experiment.title }}</h3>
              <p class="experiment-description">{{ experiment.description }}</p>
              
              <!-- 实验统计 -->
              <div class="experiment-stats">
                <div class="stat-item">
                  <el-icon><Clock /></el-icon>
                  <span>{{ experiment.duration }}分钟</span>
                </div>
                <div class="stat-item">
                  <el-icon><User /></el-icon>
                  <span>{{ experiment.completedCount }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Star /></el-icon>
                  <span>{{ experiment.rating }}</span>
                </div>
              </div>

              <!-- 实验标签 -->
              <div class="experiment-tags">
                <el-tag 
                  v-for="tag in experiment.tags" 
                  :key="tag" 
                  size="small" 
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <!-- 操作按钮 -->
              <div class="experiment-actions">
                <el-button type="primary" @click.stop="startExperiment(experiment)">
                  开始实验
                </el-button>
                <el-button @click.stop="goToExperiment(experiment.id)">
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { experimentAPI } from '../mock/api.js'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const experiments = ref([])
const searchKeyword = ref('')
const selectedSubject = ref('')
const selectedDifficulty = ref('')

// 计算统计数据
const physicsCount = computed(() => 
  experiments.value.filter(e => e.subject === '物理').length
)

const chemistryCount = computed(() => 
  experiments.value.filter(e => e.subject === '化学').length
)

const completedCount = computed(() => {
  // 这里应该从用户的实验记录中计算
  return 5 // 模拟数据
})

// 获取难度样式类
const getDifficultyClass = (difficulty) => {
  const classMap = {
    'beginner': 'difficulty-beginner',
    'intermediate': 'difficulty-intermediate',
    'advanced': 'difficulty-advanced'
  }
  return classMap[difficulty] || 'difficulty-beginner'
}

// 获取难度文本
const getDifficultyText = (difficulty) => {
  const textMap = {
    'beginner': '初级',
    'intermediate': '中级',
    'advanced': '高级'
  }
  return textMap[difficulty] || '初级'
}

// 处理学科筛选
const handleSubjectChange = () => {
  fetchExperiments()
}

// 处理难度筛选
const handleDifficultyChange = () => {
  fetchExperiments()
}

// 处理搜索
const handleSearch = () => {
  fetchExperiments()
}

// 跳转到实验详情
const goToExperiment = (experimentId) => {
  router.push(`/lab/experiment/${experimentId}`)
}

// 开始实验
const startExperiment = (experiment) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  router.push(`/lab/simulation/${experiment.id}`)
}

// 获取实验列表
const fetchExperiments = async () => {
  loading.value = true
  try {
    const params = {}
    
    if (selectedSubject.value) {
      params.subject = selectedSubject.value
    }
    
    if (selectedDifficulty.value) {
      params.difficulty = selectedDifficulty.value
    }
    
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    
    const response = await experimentAPI.getExperiments(params)
    experiments.value = response.data
  } catch (error) {
    console.error('获取实验列表失败:', error)
    ElMessage.error('获取实验列表失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await fetchExperiments()
})
</script>

<style scoped>
.laboratory-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 16px;
  color: #606266;
}

.lab-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-3px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.physics {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.chemistry {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 18px;
}

.stat-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-bar {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.experiments-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container,
.empty-container {
  padding: 40px 0;
}

.experiments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.experiment-card {
  border: 1px solid #ebeef5;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.experiment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.experiment-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.experiment-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.experiment-card:hover .experiment-cover img {
  transform: scale(1.05);
}

.experiment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.experiment-card:hover .experiment-overlay {
  opacity: 1;
}

.play-icon {
  color: white;
}

.subject-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.subject-badge.物理 {
  background: #667eea;
}

.subject-badge.化学 {
  background: #f5576c;
}

.difficulty-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.difficulty-beginner {
  background: #67c23a;
}

.difficulty-intermediate {
  background: #e6a23c;
}

.difficulty-advanced {
  background: #f56c6c;
}

.experiment-info {
  padding: 20px;
}

.experiment-title {
  font-size: 18px;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.experiment-description {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.experiment-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #909399;
}

.experiment-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 15px;
}

.experiment-actions {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 24px;
  }
  
  .lab-stats {
    grid-template-columns: 1fr;
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .experiments-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .experiment-actions {
    flex-direction: column;
  }
}
</style>
