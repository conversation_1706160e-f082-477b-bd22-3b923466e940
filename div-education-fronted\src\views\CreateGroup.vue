<template>
  <div class="create-group-page">
    <div class="container">
      <div class="page-header">
        <h1>创建学习小组</h1>
        <p>创建属于你的学习小组，与志同道合的伙伴一起学习</p>
      </div>
      
      <div class="create-form">
        <el-form
          ref="groupFormRef"
          :model="groupForm"
          :rules="groupRules"
          label-width="100px"
        >
          <el-form-item label="小组名称" prop="name">
            <el-input
              v-model="groupForm.name"
              placeholder="请输入小组名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="小组描述" prop="description">
            <el-input
              v-model="groupForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入小组描述，介绍小组的学习方向和目标"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="小组标签" prop="tags">
            <el-select
              v-model="groupForm.tags"
              multiple
              filterable
              allow-create
              placeholder="选择或输入标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in commonTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="最大成员数" prop="maxMembers">
            <el-input-number
              v-model="groupForm.maxMembers"
              :min="5"
              :max="1000"
              :step="5"
              style="width: 200px"
            />
            <span class="form-tip">建议设置为5-500人</span>
          </el-form-item>
          
          <el-form-item label="小组类型" prop="isPublic">
            <el-radio-group v-model="groupForm.isPublic">
              <el-radio :label="true">公开小组</el-radio>
              <el-radio :label="false">私密小组</el-radio>
            </el-radio-group>
            <div class="form-tip">
              公开小组：任何人都可以搜索到并申请加入<br>
              私密小组：只有通过邀请链接才能加入
            </div>
          </el-form-item>
          
          <el-form-item label="小组封面">
            <el-upload
              v-model:file-list="fileList"
              action="#"
              list-type="picture-card"
              :auto-upload="false"
              :limit="1"
              accept="image/*"
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  上传小组封面图片，建议尺寸400x300，不超过2MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
          
          <el-form-item>
            <div class="form-actions">
              <el-button @click="$router.back()">取消</el-button>
              <el-button type="primary" :loading="submitting" @click="submitGroup">
                创建小组
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { groupAPI } from '../mock/api.js'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const groupFormRef = ref()
const submitting = ref(false)
const fileList = ref([])

// 常用标签
const commonTags = [
  '前端开发', '后端开发', '移动开发', '数据科学', '人工智能',
  'Vue', 'React', 'JavaScript', 'Python', 'Java',
  '考研', '求职', '面试', '项目实战', '算法',
  '设计', 'UI/UX', '产品', '运营', '创业'
]

// 表单数据
const groupForm = reactive({
  name: '',
  description: '',
  tags: [],
  maxMembers: 100,
  isPublic: true
})

// 表单验证规则
const groupRules = {
  name: [
    { required: true, message: '请输入小组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '小组名称长度在2到50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入小组描述', trigger: 'blur' },
    { min: 10, max: 500, message: '小组描述长度在10到500个字符', trigger: 'blur' }
  ],
  tags: [
    { type: 'array', min: 1, max: 5, message: '请选择1-5个标签', trigger: 'change' }
  ],
  maxMembers: [
    { required: true, message: '请设置最大成员数', trigger: 'blur' }
  ],
  isPublic: [
    { required: true, message: '请选择小组类型', trigger: 'change' }
  ]
}

// 提交小组
const submitGroup = async () => {
  if (!groupFormRef.value) return
  
  try {
    await groupFormRef.value.validate()
    submitting.value = true
    
    // 处理封面图片上传（这里只是模拟）
    const coverImage = fileList.value.length > 0 
      ? 'https://cube.elemecdn.com/6/94/4d3ea53c4085a1c5b2c6b9bb532e7png.png'
      : 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    
    const groupData = {
      name: groupForm.name,
      description: groupForm.description,
      tags: groupForm.tags,
      maxMembers: groupForm.maxMembers,
      isPublic: groupForm.isPublic,
      coverImage: coverImage,
      creator: {
        id: userStore.user.id,
        username: userStore.user.username,
        avatar: userStore.user.avatar
      }
    }
    
    await groupAPI.createGroup(groupData)
    
    ElMessage.success('学习小组创建成功')
    router.push('/groups')
    
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('创建失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.create-group-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 16px;
}

.create-form {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
  line-height: 1.5;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .create-form {
    padding: 20px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
