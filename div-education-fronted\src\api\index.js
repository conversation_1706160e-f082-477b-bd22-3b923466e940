// API配置 - DIV教育学习平台
// 注意：项目默认使用MySQL数据库，必须启动后端服务

// API基础配置
const API_BASE_URL = 'http://localhost:8081/api'

// HTTP请求工具
const request = async (url, options = {}) => {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  }
  
  if (config.body && typeof config.body === 'object') {
    config.body = JSON.stringify(config.body)
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}${url}`, config)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // 转换后端响应格式为前端期望格式
    if (data.code === 200) {
      return {
        code: 200,
        message: data.message || 'success',
        data: data.data,
        timestamp: new Date().toISOString()
      }
    } else {
      throw new Error(data.message || '请求失败')
    }
  } catch (error) {
    console.error('❌ 后端API请求失败:', error.message)
    console.error('🔧 请确保后端服务已启动: http://localhost:8081/api')
    console.error('📋 启动命令: mvn spring-boot:run')

    // 显示用户友好的错误信息
    const errorMessage = error.message.includes('fetch')
      ? '无法连接到后端服务，请确保后端已启动'
      : `后端服务错误: ${error.message}`

    throw new Error(errorMessage)
  }
}



// 导出统一的API接口
export const api = {
  // 认证相关
  auth: {
    login: (email, password) => request('/auth/login', {
      method: 'POST',
      body: { email, password }
    }),
    register: (username, email, password, nickname, role) => request('/auth/register', {
      method: 'POST',
      body: { username, email, password, nickname, role }
    }),
    checkEmail: (email) => request(`/auth/check-email?email=${email}`),
    checkUsername: (username) => request(`/auth/check-username?username=${username}`)
  },
  
  // 用户相关
  users: {
    getUsers: (page = 0, size = 10) => request(`/users?page=${page}&size=${size}`),
    getUserById: (id) => request(`/users/${id}`),
    searchUsers: (keyword, page = 0, size = 10) => request(`/users/search?keyword=${keyword}&page=${page}&size=${size}`),
    updateUser: (id, data) => request(`/users/${id}`, {
      method: 'PUT',
      body: data
    })
  },
  
  // 课程相关
  courses: {
    getCourses: (page = 0, size = 10, category, level) => {
      let url = `/courses?page=${page}&size=${size}`
      if (category) url += `&category=${category}`
      if (level) url += `&level=${level}`
      return request(url)
    },
    getCourseById: (id) => request(`/courses/${id}`),
    searchCourses: (keyword, page = 0, size = 10) => request(`/courses/search?keyword=${keyword}&page=${page}&size=${size}`),
    getPopularCourses: (limit = 10) => request(`/courses/popular?limit=${limit}`),
    getLatestCourses: (limit = 10) => request(`/courses/latest?limit=${limit}`),
    getRecommendedCourses: (minRating = 4.0, limit = 10) => request(`/courses/recommended?minRating=${minRating}&limit=${limit}`)
  },
  
  // 实验相关
  experiments: {
    getExperiments: (page = 0, size = 10, subject, difficulty) => {
      let url = `/experiments?page=${page}&size=${size}`
      if (subject) url += `&subject=${subject}`
      if (difficulty) url += `&difficulty=${difficulty}`
      return request(url)
    },
    getExperimentById: (id) => request(`/experiments/${id}`),
    searchExperiments: (keyword, page = 0, size = 10) => request(`/experiments/search?keyword=${keyword}&page=${page}&size=${size}`),
    getPopularExperiments: (limit = 10) => request(`/experiments/popular?limit=${limit}`),
    getLatestExperiments: (limit = 10) => request(`/experiments/latest?limit=${limit}`)
  },
  
  // 帖子相关
  posts: {
    getPosts: (page = 0, size = 10, category) => {
      let url = `/posts?page=${page}&size=${size}`
      if (category) url += `&category=${category}`
      return request(url)
    },
    getPostById: (id) => request(`/posts/${id}`),
    searchPosts: (keyword, page = 0, size = 10) => request(`/posts/search?keyword=${keyword}&page=${page}&size=${size}`),
    getPopularPosts: (limit = 10) => request(`/posts/popular?limit=${limit}`),
    getLatestPosts: (limit = 10) => request(`/posts/latest?limit=${limit}`)
  }
}

// 默认导出API对象
export default api
