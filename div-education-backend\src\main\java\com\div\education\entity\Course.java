package com.div.education.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.math.BigDecimal;

/**
 * 课程实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "courses")
public class Course extends BaseEntity {

    @Column(name = "title", nullable = false, length = 200)
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "cover_image", length = 500)
    private String coverImage;

    @Column(name = "instructor", nullable = false, length = 100)
    private String instructor;

    @Column(name = "category", nullable = false, length = 50)
    private String category;

    @Column(name = "level", nullable = false, length = 20)
    private String level;

    @Column(name = "duration", nullable = false)
    private Integer duration; // 课程时长（分钟）

    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating;

    @Column(name = "student_count", nullable = false)
    private Integer studentCount = 0;

    @Column(name = "lesson_count", nullable = false)
    private Integer lessonCount = 0;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CourseStatus status = CourseStatus.DRAFT;

    @Column(name = "tags", length = 500)
    private String tags; // JSON格式存储标签

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacher_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private User teacher;

    /**
     * 课程状态枚举
     */
    public enum CourseStatus {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        ARCHIVED("已归档");

        private final String description;

        CourseStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
