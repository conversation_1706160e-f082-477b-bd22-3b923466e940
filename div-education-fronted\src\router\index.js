import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
const HomePage = () => import('../views/HomePage.vue')
const LearningCenter = () => import('../views/LearningCenter.vue')
const CourseDetail = () => import('../views/CourseDetail.vue')
const Community = () => import('../views/Community.vue')
const PostDetail = () => import('../views/PostDetail.vue')
const CreatePost = () => import('../views/CreatePost.vue')
const News = () => import('../views/News.vue')
const StudyGroups = () => import('../views/StudyGroups.vue')
const GroupDetail = () => import('../views/GroupDetail.vue')
const CreateGroup = () => import('../views/CreateGroup.vue')
const Profile = () => import('../views/Profile.vue')
const Login = () => import('../views/Login.vue')
const Register = () => import('../views/Register.vue')
const Laboratory = () => import('../views/Laboratory.vue')
const ExperimentDetail = () => import('../views/ExperimentDetail.vue')
const ExperimentSimulation = () => import('../views/ExperimentSimulation.vue')
const Analytics = () => import('../views/Analytics.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage,
    meta: {
      title: 'DIV教育学习平台 - 首页'
    }
  },
  {
    path: '/learning',
    name: 'LearningCenter',
    component: LearningCenter,
    meta: {
      title: '学习中心'
    }
  },
  {
    path: '/learning/course/:id',
    name: 'CourseDetail',
    component: CourseDetail,
    meta: {
      title: '课程详情'
    }
  },
  {
    path: '/community',
    name: 'Community',
    component: Community,
    meta: {
      title: '交流社区'
    }
  },
  {
    path: '/community/post/:id',
    name: 'PostDetail',
    component: PostDetail,
    meta: {
      title: '帖子详情'
    }
  },
  {
    path: '/community/create',
    name: 'CreatePost',
    component: CreatePost,
    meta: {
      title: '发布帖子',
      requiresAuth: true
    }
  },
  {
    path: '/news',
    name: 'News',
    component: News,
    meta: {
      title: '简讯'
    }
  },
  {
    path: '/groups',
    name: 'StudyGroups',
    component: StudyGroups,
    meta: {
      title: '学习小组'
    }
  },
  {
    path: '/groups/:id',
    name: 'GroupDetail',
    component: GroupDetail,
    meta: {
      title: '小组详情'
    }
  },
  {
    path: '/groups/create',
    name: 'CreateGroup',
    component: CreateGroup,
    meta: {
      title: '创建小组',
      requiresAuth: true
    }
  },
  {
    path: '/lab',
    name: 'Laboratory',
    component: Laboratory,
    meta: {
      title: '仿真实验'
    }
  },
  {
    path: '/lab/experiment/:id',
    name: 'ExperimentDetail',
    component: ExperimentDetail,
    meta: {
      title: '实验详情'
    }
  },
  {
    path: '/lab/simulation/:id',
    name: 'ExperimentSimulation',
    component: ExperimentSimulation,
    meta: {
      title: '实验模拟',
      requiresAuth: true
    }
  },
  {
    path: '/analytics',
    name: 'Analytics',
    component: Analytics,
    meta: {
      title: '数据分析',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人中心',
      requiresAuth: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '用户注册'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (!token) {
      next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  next()
})

export default router
