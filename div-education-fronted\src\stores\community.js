import { defineStore } from 'pinia'
import { api } from '../api/index.js'

export const useCommunityStore = defineStore('community', {
  state: () => ({
    // 帖子列表
    posts: [],
    // 当前帖子
    currentPost: null,
    // 评论列表
    comments: [],
    // 用户发布的帖子
    userPosts: [],
    // 加载状态
    loading: false,
    // 分页信息
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    }
  }),

  getters: {
    // 获取热门帖子
    hotPosts: (state) => {
      return state.posts
        .filter(post => post.likesCount > 50)
        .sort((a, b) => b.likesCount - a.likesCount)
        .slice(0, 5)
    },
    
    // 获取最新帖子
    latestPosts: (state) => {
      return state.posts
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 10)
    }
  },

  actions: {
    // 获取帖子列表
    async fetchPosts(params = {}) {
      this.loading = true
      try {
        const response = await communityAPI.getPosts({
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...params
        })
        
        this.posts = response.data.list
        this.pagination.total = response.data.total
        this.pagination.page = response.data.page
        
        return response
      } catch (error) {
        console.error('获取帖子列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取帖子详情
    async fetchPostDetail(postId) {
      this.loading = true
      try {
        const response = await communityAPI.getPostDetail(postId)
        this.currentPost = response.data
        return response
      } catch (error) {
        console.error('获取帖子详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 发布帖子
    async createPost(postData) {
      try {
        const response = await communityAPI.createPost(postData)
        
        // 将新帖子添加到列表开头
        this.posts.unshift(response.data)
        this.pagination.total += 1
        
        return response
      } catch (error) {
        console.error('发布帖子失败:', error)
        throw error
      }
    },

    // 点赞帖子
    async likePost(postId) {
      try {
        const response = await communityAPI.likePost(postId)
        
        // 更新本地帖子列表中的点赞状态
        const postIndex = this.posts.findIndex(p => p.id === postId)
        if (postIndex !== -1) {
          this.posts[postIndex] = response.data
        }
        
        // 如果是当前帖子，也要更新
        if (this.currentPost?.id === postId) {
          this.currentPost = response.data
        }
        
        return response
      } catch (error) {
        console.error('点赞操作失败:', error)
        throw error
      }
    },

    // 获取评论列表
    async fetchComments(postId) {
      try {
        const response = await communityAPI.getComments(postId)
        this.comments = response.data
        return response
      } catch (error) {
        console.error('获取评论列表失败:', error)
        throw error
      }
    },

    // 发表评论
    async createComment(commentData) {
      try {
        const response = await communityAPI.createComment(commentData)
        
        // 将新评论添加到列表
        this.comments.push(response.data)
        
        // 更新帖子的评论数
        if (this.currentPost?.id === commentData.postId) {
          this.currentPost.commentsCount += 1
        }
        
        const postIndex = this.posts.findIndex(p => p.id === commentData.postId)
        if (postIndex !== -1) {
          this.posts[postIndex].commentsCount += 1
        }
        
        return response
      } catch (error) {
        console.error('发表评论失败:', error)
        throw error
      }
    },

    // 搜索帖子
    async searchPosts(keyword) {
      return await this.fetchPosts({ keyword })
    },

    // 设置分页
    setPagination(page, pageSize) {
      this.pagination.page = page
      this.pagination.pageSize = pageSize
    },

    // 重置社区状态
    resetCommunityState() {
      this.posts = []
      this.currentPost = null
      this.comments = []
      this.userPosts = []
      this.pagination = {
        page: 1,
        pageSize: 10,
        total: 0
      }
    }
  }
})
